# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# API Keys and Secrets
.env
.env.local
*.key
config/secrets.json

# Test Results and Temporary Files
*.log
*.tmp
temp/
tmp/

# Project Specific
real_model_results/
quick_results/
flagship_results/
results/*.json
!results/sample_benchmark.json

# Jupyter Notebooks
.ipynb_checkpoints/
*.ipynb

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# MacOS
.DS_Store
.AppleDouble
.LSOverride